
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React Dashboard App</title>
    <meta name="description" content="A React.js dashboard with authentication, featuring a login page, dynamic sidebar, and API-driven transaction data." />
    <link rel="icon" type="image/svg+xml" href="/placeholder.svg" />

    <meta property="og:title" content="React Dashboard App" />
    <meta property="og:description" content="A React.js dashboard with authentication, featuring a login page, dynamic sidebar, and API-driven transaction data." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/og.jpg" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="/og.jpg" />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
