const BASE_URL = import.meta.env.VITE_BASE_API_URL;

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  website: string;
  phone: string;
  company: {
    name: string;
  };
}

export const apiService = {
  async getUsers(): Promise<User[]> {
    try {
      const response = await fetch(`${BASE_URL}/users`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }
};

// Auth service
export const authService = {
  login(username: string, password: string): boolean {
    return username === 'admin' && password === 'admin@123';
  },
  
  logout(): void {
    localStorage.removeItem('isAuthenticated');
  },
  
  isAuthenticated(): boolean {
    return localStorage.getItem('isAuthenticated') === 'true';
  },
  
  setAuthenticated(value: boolean): void {
    localStorage.setItem('isAuthenticated', value.toString());
  }
};