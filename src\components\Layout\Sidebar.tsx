import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { LayoutDashboard, CreditCard, Building2 } from 'lucide-react';

const Sidebar = () => {
  const location = useLocation();

  const menuItems = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'All Transactions',
      path: '/transactions',
      icon: CreditCard,
    },
  ];

  return (
    <div className="w-64 bg-gradient-to-b from-slate-100 to-slate-200 border-r border-slate-300/50 h-full shadow-lg">
      {/* Header Section */}
      <div className="p-6 border-b border-slate-300/30">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-md">
            <Building2 className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white drop-shadow-sm">LG Dashboard</h2>
            <p className="text-sm text-white/80">Admin Panel</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-6 px-3">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;

          return (
            <Link
              key={item.path}
              to={item.path}
              className={cn(
                'flex items-center px-4 py-3 mx-1 mb-2 rounded-lg text-white/90 hover:bg-white/20 hover:text-white hover:shadow-md transition-all duration-200 group',
                isActive && 'bg-white/25 text-white shadow-md border-l-4 border-blue-400 font-medium'
              )}
            >
              <Icon className={cn(
                "w-5 h-5 mr-3 transition-transform duration-200",
                "group-hover:scale-110",
                isActive && "text-blue-100"
              )} />
              <span className="font-medium">{item.name}</span>
              {isActive && (
                <div className="ml-auto w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
              )}
            </Link>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20">
          <p className="text-xs text-white/70 text-center">
            © 2024 LG Dashboard
          </p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;